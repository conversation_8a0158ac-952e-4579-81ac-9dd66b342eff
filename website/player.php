<?php
require_once __DIR__ . '/includes/layout-unified.php';
require_once __DIR__ . '/includes/db_access.php';
require_once __DIR__ . '/includes/api_client.php';
require_once __DIR__ . '/includes/mongo_security.php';

function formatNumber($number) {
    return number_format($number);
}


function calculateKDR($kills, $deaths) {
    if ($deaths === 0) {
        return $kills > 0 ? number_format($kills, 2) : "0.00"; // Avoid division by zero
    }
    return number_format($kills / $deaths, 2);
}


function formatBalance($balance) {
    return "$" . number_format($balance); // Adds $ and formats with commas
}


function formatPlaytime($minutes) {
    if (!$minutes) return "0h";

    $hours = floor($minutes / 60);
    $days = floor($hours / 24);
    $hours = $hours % 24;

    if ($days > 0) {
        return $days . "d " . $hours . "h";
    } else {
        return $hours . "h";
    }
}


/**
 * Get player leaderboard positions using API data
 */
function getPlayerLeaderboardPositionsFromAPI($currentPlayer, $apiClient) {
    $positions = [];

    try {
        // For now, return empty positions since we'd need to fetch all players from API
        // This would be expensive, so we'll implement this later or use a different approach
        error_log("Leaderboard positions calculation using API not yet implemented");
        return [];

    } catch (Exception $e) {
        error_log("Error calculating leaderboard positions from API: " . $e->getMessage());
        return [];
    }
}

function getPlayerLeaderboardPositions($username, $db) {
    $positions = [];

    try {
        // Define the categories and their corresponding database fields
        $categories = [
            'kills' => 'games.factions.stats.kills',
            'balance' => 'games.factions.stats.doubloons',
            'deaths' => 'games.factions.stats.deaths'
        ];

        foreach ($categories as $category => $field) {
            try {
                // Get all players sorted by this stat
                $cursor = $db->db->player_data->find(
                    [], // No filter - get all players
                    [
                        'projection' => [
                            'playernames' => 1,
                            $field => 1
                        ],
                        'sort' => [$field => -1] // Sort descending (highest first)
                    ]
                );

                $position = 1;
                foreach ($cursor as $player) {
                    // Get the player's username
                    $playerUsername = null;
                    if (isset($player['playernames']) && is_array($player['playernames']) && !empty($player['playernames'])) {
                        $playerUsername = $player['playernames'][0];
                    }

                    if ($playerUsername && strtolower($playerUsername) === strtolower($username)) {
                        $positions[$category] = $position;
                        error_log("Found {$username} at position {$position} in {$category} leaderboard");
                        break;
                    }
                    $position++;
                }

                // If player not found in leaderboard, log it
                if (!isset($positions[$category])) {
                    error_log("Player {$username} not found in {$category} leaderboard");
                }
            } catch (Exception $e) {
                error_log("Error getting {$category} leaderboard position: " . $e->getMessage());
                continue;
            }
        }

        // Calculate KDR separately since it requires computation
        try {
            $cursor = $db->db->player_data->find(
                [], // No filter - get all players
                [
                    'projection' => [
                        'playernames' => 1,
                        'games.factions.stats.kills' => 1,
                        'games.factions.stats.deaths' => 1
                    ]
                ]
            );

            $kdrData = [];
            foreach ($cursor as $player) {
                $playerUsername = null;
                if (isset($player['playernames']) && is_array($player['playernames']) && !empty($player['playernames'])) {
                    $playerUsername = $player['playernames'][0];
                }

                if ($playerUsername) {
                    $kills = $player['games']['factions']['stats']['kills'] ?? 0;
                    $deaths = $player['games']['factions']['stats']['deaths'] ?? 0;
                    $kdr = ($deaths === 0) ? $kills : $kills / $deaths;

                    $kdrData[] = [
                        'username' => $playerUsername,
                        'kdr' => $kdr
                    ];
                }
            }

            // Sort by KDR descending
            usort($kdrData, function($a, $b) {
                return $b['kdr'] <=> $a['kdr'];
            });

            // Find the player's position
            $position = 1;
            foreach ($kdrData as $player) {
                if (strtolower($player['username']) === strtolower($username)) {
                    $positions['kdr'] = $position;
                    error_log("Found {$username} at position {$position} in KDR leaderboard");
                    break;
                }
                $position++;
            }

            if (!isset($positions['kdr'])) {
                error_log("Player {$username} not found in KDR leaderboard");
            }
        } catch (Exception $e) {
            error_log("Error calculating KDR leaderboard position: " . $e->getMessage());
        }

    } catch (Exception $e) {
        error_log("Error in getPlayerLeaderboardPositions: " . $e->getMessage());
    }

    return $positions;
}

function convertToET($timestamp) {
    if (!$timestamp) return "Unknown";


    if (is_object($timestamp)) {

        if (method_exists($timestamp, 'toDateTime')) {
            try {

                $dateTime = $timestamp->toDateTime();


                $dateTime->setTimezone(new DateTimeZone('America/New_York'));


                return $dateTime->format('F j, Y \a\t g:i a T');
            } catch (Exception $e) {
                error_log("Error converting UTCDateTime: " . $e->getMessage());
                return "Unknown";
            }
        }


        if (get_class($timestamp) === 'MongoDB\Model\BSONDocument') {
            try {

                $dateArray = $timestamp->getArrayCopy();
                if (isset($dateArray['$date'])) {

                    $dateTime = new DateTime($dateArray['$date']);


                    $dateTime->setTimezone(new DateTimeZone('America/New_York'));


                    return $dateTime->format('F j, Y \a\t g:i a T');
                }
            } catch (Exception $e) {
                error_log("Error parsing BSON document date: " . $e->getMessage());
            }

            return "Unknown";
        }
        return "Unknown";
    }


    if (is_array($timestamp) && isset($timestamp['$date'])) {
        try {

            $dateTime = new DateTime($timestamp['$date']);


            $dateTime->setTimezone(new DateTimeZone('America/New_York'));


            return $dateTime->format('F j, Y \a\t g:i a T');
        } catch (Exception $e) {
            error_log("Error parsing MongoDB extended JSON date: " . $e->getMessage());
            return "Unknown";
        }
    }


    if (is_string($timestamp)) {
        // Handle ISODate format first: ISODate('2025-07-25T14:33:27.249Z')
        if (preg_match('/ISODate\([\'"]([^\'"]+)[\'"]\)/', $timestamp, $matches)) {
            try {
                $dateTime = new DateTime($matches[1]);
                $dateTime->setTimezone(new DateTimeZone('America/New_York'));
                return $dateTime->format('F j, Y \a\t g:i a T');
            } catch (Exception $e) {
                error_log("Error parsing ISODate format: " . $e->getMessage());
            }
        }

        $time = strtotime($timestamp);
        if ($time) {

            $dateTime = new DateTime();
            $dateTime->setTimestamp($time);


            $dateTime->setTimezone(new DateTimeZone('America/New_York'));


            return $dateTime->format('F j, Y \a\t g:i a T');
        }


        if (strpos($timestamp, '$date') !== false || strpos($timestamp, 'ISODate') !== false) {
            try {

                if (preg_match('/\$date"\s*:\s*"?([^"}]+)"?/', $timestamp, $matches)) {
                    $dateTime = new DateTime($matches[1]);
                } elseif (preg_match('/ISODate\([\'"]([^\'"]+)[\'"]\)/', $timestamp, $matches)) {
                    $dateTime = new DateTime($matches[1]);
                } else {
                    return "Unknown";
                }


                $dateTime->setTimezone(new DateTimeZone('America/New_York'));


                return $dateTime->format('F j, Y \a\t g:i a T');
            } catch (Exception $e) {
                error_log("Error parsing MongoDB JSON date: " . $e->getMessage());
                return "Unknown";
            }
        }



        return "Unknown";
    }

    return "Unknown";
}


$rank_colors = [
    "player" => "",
    "c" => "#00FF00",
    "builder" => "#000000",
    "admin" => "#0000FF",
    "mlp" => "#ADD8E6",
    "yt" => "#FF0000",
    "youtuber" => "linear-gradient(to right, #FF0000 50%, #FFFFFF 50%)",
    "support" => "#D3D3F7",
    "moderator" => "#00FF00",
    "vip" => "#90EE90",
    "owner" => "#ADD8E6",
    "mgp" => "#8B0000",
    "mvp" => "#FFFF00",
    "trainee" => "#FFFFFF",
    "mmp" => "#FFFF00",
    "secretary" => "#FFC0CB", // Pink color
];


function getRankColor($rankName, $rank_colors) {
    $rankLower = strtolower($rankName);
    return isset($rank_colors[$rankLower]) ? $rank_colors[$rankLower] : "#FFFFFF"; // Default to white if rankName is not found
}


$username = isset($_GET['username']) ?
    sanitize_mongo_input($_GET['username'], 'string', [
        'max_length' => 100,
        'allow_empty' => false
    ]) : null;

if ($username) {
    try {
        // First get the player UUID from database for API call
        $db = new DatabaseAccess();
        $uuid_result = $db->get_player_uuid($username);

        if (isset($uuid_result['error'])) {
            $player = ['error' => $uuid_result['error']];
        } else {
            // Use API client to get up-to-date player data
            $apiClient = new ApiClient();
            $player = $apiClient->getPlayerData($uuid_result['uuid']);

            if (!isset($player['error'])) {
                error_log("Successfully loaded player {$username} from API");
            } else {
                error_log("API Error loading player {$username}: " . $player['error']);
            }
        }
        }


        if (isset($player['error']) || (isset($player[0]) && isset($player[0]['error']))) {
            renderHeader('Player Not Found');
            renderNavbar();

            $errorMessage = isset($player['error']) ? $player['error'] : $player[0]['error'];

            echo "<div class='container text-center mt-5' id='player-error-container'>
                    <div class='alert alert-danger'>
                        <h1>Player Not Found</h1>
                        <p id='player-error-message'>This player is no longer found in our database.</p>
                        <p class='small text-muted'>The player may have changed their name or has not played on the server recently.</p>
                    </div>
                    <div class='mt-4'>
                        <button class='btn btn-primary me-2' onclick='window.location.href=\"/search\"'>Back to Search</button>
                        <button class='btn btn-outline-secondary' onclick='window.location.href=\"/\"'>Return to Home</button>
                    </div>
                  </div>";

            echo "<script>
                    document.addEventListener('DOMContentLoaded', function() {
                        if (typeof handlePlayerError === 'function') {
                            handlePlayerError('" . htmlspecialchars($username, ENT_QUOTES) . "', 'Player not found');
                        }
                    });
                  </script>";

            renderFooter([
                'https://cdn.jsdelivr.net/npm/sweetalert2@11',
                '/js/search.min.js?v=1'
            ]);
            exit;
        }


        $punishments = $db->get_player_punishments($username);


        // Map API field names to expected format for compatibility
        if (isset($player['FirstLogin'])) {
            $player['first_login'] = $player['FirstLogin'];
        } elseif (isset($player['firstlogin'])) {
            $player['first_login'] = $player['firstlogin'];
        }

        if (isset($player['LastLogin'])) {
            $player['last_login'] = $player['LastLogin'];
        } elseif (isset($player['lastlogin'])) {
            $player['last_login'] = $player['lastlogin'];
        }

        if (!isset($player['first_login']) || !$player['first_login']) {
            $player['first_login'] = [
                '$date' => '2025-02-02T01:18:55.320Z'
            ];
        }

        if (!isset($player['last_login']) || !$player['last_login']) {
            $player['last_login'] = [
                '$date' => '2025-04-08T02:03:19.077Z'
            ];
        }


        $player['punishments'] = $punishments;


        // Map rankid to rank for display
        // Check all possible locations for rankid (API uses different field names)
        $rankid = null;
        if (isset($player['RankId'])) {
            $rankid = $player['RankId']; // Go API field name
        } elseif (isset($player['rankid'])) {
            $rankid = $player['rankid']; // Lowercase version
        } elseif (isset($player['groupsettings']['rankid'])) {
            $rankid = $player['groupsettings']['rankid']; // Database field
        }

        if ($rankid) {
            $player['rank'] = $rankid; // Copy rankid to rank for compatibility
            $rank = strtolower($player['rank']);
            switch ($rank) {
                case "owner":
                    $player['rank'] = "Owner";
                    break;
                case "admin":
                    $player['rank'] = "Admin";
                    break;
                case "moderator":
                    $player['rank'] = "Moderator";
                    break;
                case "builder":
                    $player['rank'] = "Builder";
                    break;
                case "vip":
                    $player['rank'] = "VIP";
                    break;
                case "mvp":
                    $player['rank'] = "MVP";
                    break;
                case "yt":
                    $player['rank'] = "YouTube";
                    break;
                case "youtuber":
                    $player['rank'] = "YouTuber";
                    break;
                case "support":
                    $player['rank'] = "Support";
                    break;
                case "trainee":
                    $player['rank'] = "Trainee";
                    break;
                case "mlp":
                    $player['rank'] = "MLP";
                    break;
                case "mgp":
                    $player['rank'] = "MGP";
                    break;
                case "mmp":
                    $player['rank'] = "MMP";
                    break;
                case "secretary":
                    $player['rank'] = "Secretary";
                    break;
                default:
                    $player['rank'] = ucfirst($rank); // Default to capitalizing the first letter
                    break;
            }
        } else {
            // Set default rank if rankid is not present
            $player['rank'] = "Player";
            error_log("No rankid found, setting default rank to Player");
        }

        // Debug: Log the final rank that was set
        error_log("Final rank set for player {$username}: " . ($player['rank'] ?? 'NOT SET'));

        // Map API faction data to expected format
        if (isset($player['Faction'])) {
            $player['faction'] = [];

            // Map faction name
            if (isset($player['Faction']['Name'])) {
                $player['faction']['name'] = $player['Faction']['Name'];
            }

            // Map faction role
            if (isset($player['Faction']['Role'])) {
                $player['faction']['role'] = $player['Faction']['Role'];
            }

            // Map faction stats
            if (isset($player['Faction']['Stats'])) {
                $player['faction']['stats'] = [];
                $stats = $player['Faction']['Stats'];

                // Map all the stats from API format to expected format
                $player['faction']['stats']['doubloons'] = $stats['Doubloons'] ?? 0;
                $player['faction']['stats']['strength'] = $stats['Strength'] ?? 0;
                $player['faction']['stats']['kills'] = $stats['Kills'] ?? 0;
                $player['faction']['stats']['deaths'] = $stats['Deaths'] ?? 0;
                $player['faction']['stats']['killstreak'] = $stats['KillStreak'] ?? 0;
                $player['faction']['stats']['bestkillstreak'] = $stats['BestKillStreak'] ?? 0;

                error_log("Mapped faction stats for {$username}: " . json_encode($player['faction']['stats']));
            }
        }

        // Calculate leaderboard positions using database method (more efficient)
        try {
            $player['leaderboard_positions'] = getPlayerLeaderboardPositions($username, $db);
            error_log("Calculated leaderboard positions for {$username}: " . json_encode($player['leaderboard_positions']));
        } catch (Exception $e) {
            error_log("Error calculating leaderboard positions for {$username}: " . $e->getMessage());
            $player['leaderboard_positions'] = [];
        }


        renderHeader("$username's Stats", [
    '/css/players-new.css',
    '/css/punishment-redesign.css'
]);


        echo '<style>
            /* Rank Badge Styles */
            .rank-badge {
                display: inline-block;
                padding: 0.25rem 0.5rem;
                border-radius: 0.25rem;
                font-size: 0.75rem;
                font-weight: bold;
                color: white;
                vertical-align: middle;
                text-transform: uppercase;
            }
            h1 .rank-badge {
                font-size: 0.8rem;
                vertical-align: middle;
                position: relative;
                top: -0.25rem;
            }

            /* Punishment Section Styles */
            .punishment-filter-group .btn {
                border-radius: 0.5rem;
                margin: 0 2px;
                transition: all 0.2s ease;
                font-weight: 500;
            }

            .punishment-filter-group .btn.active {
                box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
                transform: translateY(-2px);
            }

            .punishment-card-container {
                border-radius: 0.5rem;
                overflow: hidden;
                transition: all 0.3s ease;
                border-width: 2px;
            }

            .punishment-card-container:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
            }

            .punishment-header {
                padding: 0.75rem 1rem;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            .punishment-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 36px;
                height: 36px;
                border-radius: 50%;
                background-color: rgba(0, 0, 0, 0.2);
            }

            .collapse-icon {
                transition: transform 0.3s ease;
            }

            [aria-expanded="true"] .collapse-icon {
                transform: rotate(180deg);
            }

            .punishment-details {
                background-color: rgba(0, 0, 0, 0.2);
                padding: 1.25rem;
            }

            .bg-dark-secondary {
                background-color: rgba(0, 0, 0, 0.15);
            }

            .detail-item {
                padding: 0.5rem 0;
            }

            .text-orange {
                color: #fd7e14;
            }

            .border-orange {
                border-color: #fd7e14 !important;
            }

            /* Reason preview styling */
            .reason-preview {
                max-width: 250px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                opacity: 0.8;
            }

            /* Improved reason display */
            .reason {
                position: relative;
                background-color: rgba(0, 0, 0, 0.2) !important;
                border-left: 3px solid rgba(255, 255, 255, 0.2);
                font-style: italic;
            }

            /* Player Join Info Styles */
            .join-info-item {
                margin-bottom: 0.5rem;
                padding: 8px;
                border-radius: 6px;
                background-color: rgba(0, 0, 0, 0.15);
                transition: background-color 0.3s;
            }

            .join-info-item:hover {
                background-color: rgba(0, 0, 0, 0.25);
            }

            @media (max-width: 767.98px) {
                .player-join-info .row > div {
                    margin-bottom: 10px;
                }

                .player-join-info .row > div:last-child {
                    margin-bottom: 0;
                }

                .join-info-item {
                    display: inline-block;
                    width: 100%;
                }
            }
        </style>';
        renderNavbar();

        renderHeader('Database Error');
        renderNavbar();

        echo "<div class='container text-center mt-5' id='api-error-container'>
                <div class='alert alert-danger'>
                    <h1>Database Error</h1>
                    <p id='api-error-message'>There was an error connecting to the database. Please try again later.</p>
                    <p class='small'>" . htmlspecialchars($e->getMessage()) . "</p>
                </div>
              </div>";

        renderFooter([
            'https://cdn.jsdelivr.net/npm/sweetalert2@11',
            '/js/search.min.js?v=1'
        ]);
        exit;
    }
    ?>
    <div class="container">
        <div class="card bg-dark text-light border-0 shadow-lg">
            <header class="text-center mb-4 position-relative">
                <div class="faction-banner rounded-top p-4" style="background: linear-gradient(135deg, #1e2a45, #101828);">
                    <h1 class="display-5 fw-bold text-light mb-0">
                        <?php if (isset($player['rank']) && $player['rank'] && strtolower($player['rank']) !== "player") { ?>
                            <span class="rank-badge me-2" style="background-color: <?php echo getRankColor(strtolower($player['rank']), $rank_colors); ?>; display: inline-block; vertical-align: middle; font-size: 0.5em; position: relative; top: -0.1em;">
                                <?php echo strtoupper($player['rank']); ?>
                            </span>
                        <?php } ?>
                        <?php echo $username; ?>
                    </h1>
                    <div class="player-join-info mt-3">
                        <div class="row justify-content-center">
                            <div class="col-md-4 col-sm-12 text-center mb-2 mb-md-0">
                                <div class="join-info-item">
                                    <i class="fas fa-calendar-plus text-info me-2"></i>
                                    <span class="text-muted">First Join:</span>
                                    <span class="text-light">
                                        <?php echo convertToET($player['first_login']); ?>
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12 text-center mb-2 mb-md-0">
                                <div class="join-info-item">
                                    <?php
                                    // Get online status from API data
                                    $isOnline = false;

                                    // Check for Go API field name first, then fallback to database field name
                                    if (isset($player['Online'])) {
                                        $isOnline = (bool)$player['Online'];
                                        error_log("Player {$username} online status from API (Online): " . ($isOnline ? "true" : "false"));
                                    } elseif (isset($player['online'])) {
                                        $isOnline = (bool)$player['online'];
                                        error_log("Player {$username} online status from API (online): " . ($isOnline ? "true" : "false"));
                                    } else {
                                        error_log("Player {$username} online status not found in API response");
                                    }

                                    if ($isOnline) {
                                    ?>
                                        <span class="badge bg-success" style="font-size: 1rem; padding: 0.5rem 1rem;">ONLINE</span>
                                    <?php } else { ?>
                                        <span class="badge bg-danger" style="font-size: 1rem; padding: 0.5rem 1rem;">OFFLINE</span>
                                    <?php } ?>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12 text-center">
                                <div class="join-info-item">
                                    <i class="fas fa-calendar-check text-success me-2"></i>
                                    <span class="text-muted">Last Login:</span>
                                    <span class="text-light">
                                        <?php echo convertToET($player['last_login']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </header>

            <!-- Loading spinner (hidden by default) -->
            <div id="loading" class="text-center py-5" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="text-light mt-3">Loading player data...</p>
            </div>

            <!-- Error message -->
            <div id="error-message" style="display: none;" class="alert alert-danger mx-4 mb-4">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <span id="error-text">Player not found</span>
            </div>

            <!-- Player content -->
            <div id="player-content" class="card-body p-0">


                <!-- Player Stats Section -->
                <div class="stats-overview px-4 pb-4 mb-4 border-bottom border-secondary">
                    <h4 class="section-title"><i class="fas fa-chart-bar me-2"></i>Player Statistics</h4>

                    <!-- Combat Stats -->
                    <div class="stats-category mb-4">
                        <h5 class="stats-category-title"><i class="fas fa-crosshairs me-2"></i>Combat</h5>
                        <div class="row g-3">
                            <div class="col-md-3 col-6">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-skull"></i>
                                    </div>
                                    <div class="stat-details">
                                        <h6 class="stat-title">Kills</h6>
                                        <p class="stat-value"><?php echo number_format($player['faction']['stats']['kills'] ?? 0); ?></p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-6">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-heart-broken"></i>
                                    </div>
                                    <div class="stat-details">
                                        <h6 class="stat-title">Deaths</h6>
                                        <p class="stat-value"><?php echo number_format($player['faction']['stats']['deaths'] ?? 0); ?></p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-6">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-bolt"></i>
                                    </div>
                                    <div class="stat-details">
                                        <h6 class="stat-title">Killstreak</h6>
                                        <p class="stat-value"><?php echo number_format($player['faction']['stats']['killstreak'] ?? 0); ?></p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-6">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="stat-details">
                                        <h6 class="stat-title">KDR</h6>
                                        <p class="stat-value"><?php echo calculateKDR($player['faction']['stats']['kills'] ?? 0, $player['faction']['stats']['deaths'] ?? 0); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Economy Stats -->
                    <div class="stats-category">
                        <h5 class="stats-category-title"><i class="fas fa-money-bill-wave me-2"></i>Economy</h5>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-coins"></i>
                                    </div>
                                    <div class="stat-details">
                                        <h6 class="stat-title">Balance</h6>
                                        <p class="stat-value"><?php echo formatBalance($player['faction']['stats']['doubloons'] ?? 0); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-skull-crossbones"></i>
                                    </div>
                                    <div class="stat-details">
                                        <h6 class="stat-title">Bounty</h6>
                                        <p class="stat-value">
                                            <?php
                                            if (isset($player['bounty']) && $player['bounty'] > 0) {
                                                echo formatBalance($player['bounty']);

                                                // Add bounty source if available
                                                if (isset($player['bounty_source'])) {
                                                }
                                            } else {
                                                echo '$0';
                                            }
                                            ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Leaderboard Positions Section -->
                <div class="leaderboard-section px-4 pb-4 mb-4 border-bottom border-secondary">
                    <h4 class="section-title"><i class="fas fa-list-ol me-2"></i>Leaderboard Positions</h4>
                    <div class="row g-3">
                        <?php
                        function addOrdinalSuffix($number) {
                            if ($number === null || $number === 0) {
                                return 'N/A';
                            }

                            $suffixes = ['th', 'st', 'nd', 'rd'];
                            $value = $number % 100;

                            if ($value >= 11 && $value <= 13) {
                                return $number . 'th';
                            } else {
                                $lastDigit = $value % 10;
                                switch ($lastDigit) {
                                    case 1:  return $number . 'st';
                                    case 2:  return $number . 'nd';
                                    case 3:  return $number . 'rd';
                                    default: return $number . 'th';
                                }
                            }
                        }

                        // Define all categories to show, even if player doesn't have a position
                        $allCategories = [
                            'kills' => 'fa-crosshairs',
                            'balance' => 'fa-coins',
                            'deaths' => 'fa-skull',
                            'kdr' => 'fa-chart-line'
                        ];

                        $playerPositions = $player['leaderboard_positions'] ?? [];

                        foreach ($allCategories as $category => $icon) {
                            $position = $playerPositions[$category] ?? null;
                            $displayValue = addOrdinalSuffix($position);
                            $clickAction = ($position !== null && $position > 0) ? "triggerConfetti({$position})" : "";
                        ?>
                            <div class="col-md-4">
                                <div class="stat-card" <?php if ($clickAction) echo "onclick=\"{$clickAction}\""; ?>>
                                    <div class="stat-icon">
                                        <i class="fas <?php echo $icon; ?>"></i>
                                    </div>
                                    <div class="stat-details">
                                        <h6 class="stat-title"><?php echo strtoupper($category) === 'KDR' ? 'KDR' : ucfirst($category); ?> Rank</h6>
                                        <p class="stat-value <?php echo ($position === null) ? 'text-muted' : ''; ?>"><?php echo $displayValue; ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>

                <!-- Punishment Section - Redesigned -->
                <div class="punishment-section px-4 pb-4">
                    <h4 class="section-title"><i class="fas fa-gavel me-2"></i>Punishment History</h4>

                    <?php if (!isset($player['punishments']) || count($player['punishments']) === 0) { ?>
                    <!-- No Punishments Message -->
                    <div class="no-punishments-container">
                        <div class="no-punishments-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h5 class="text-success mb-3">No Punishments Found</h5>
                        <p class="text-muted mb-0">This player has a clean record with no punishment history.</p>
                    </div>
                    <?php } else { ?>

                    <!-- Punishment Timeline -->
                    <div class="punishment-timeline">
                        <?php

                        foreach ($player['punishments'] as $index => $punishment) {

                            $punishmentType = strtoupper($punishment['type'] ?? $punishment['punishment_type'] ?? '');


                            $isActive = $punishment['active'] ?? false;


                            $cardClass = '';
                            $typeBadgeClass = '';
                            $iconClass = 'fa-exclamation-circle';

                            switch($punishmentType) {
                                case 'BAN':
                                    $iconClass = 'fa-ban';
                                    $typeBadgeClass = 'ban';
                                    $cardClass = $isActive ? 'active' : '';
                                    break;
                                case 'MUTE':
                                    $iconClass = 'fa-volume-mute';
                                    $typeBadgeClass = 'mute';
                                    $cardClass = $isActive ? 'mute' : '';
                                    break;
                                case 'WARNING':
                                case 'WARN':
                                    $iconClass = 'fa-exclamation-triangle';
                                    $typeBadgeClass = 'warning';
                                    $cardClass = $isActive ? 'warning' : '';
                                    break;
                            }


                            $offenseType = isset($punishment['offense_type']) ? $punishment['offense_type'] : '';
                            $offenseCount = isset($punishment['offense_count']) ? $punishment['offense_count'] : 0;


                            error_log("Punishment data: " . json_encode($punishment));
                            error_log("Punishment type: " . $punishmentType);
                            error_log("Offense type: " . $offenseType);


                            $offenseTypeDisplay = '';
                            if (!empty($offenseType)) {

                                if (stripos($offenseType, 'hacking') !== false || stripos($offenseType, 'cheating') !== false) {

                                    $offenseTypeDisplay = "Violation";
                                } else {

                                    $offenseTypeDisplay = str_replace('_', ' ', $offenseType);
                                    $offenseTypeDisplay = ucwords($offenseTypeDisplay);
                                }
                            }


                            $issuedDate = convertToET($punishment['date'] ?? $punishment['issued_at'] ?? '');
                            $expiresDate = isset($punishment['expires']) || isset($punishment['expires_at'])
                                ? $punishment['expires'] ?? $punishment['expires_at'] ?? null
                                : null;
                        ?>
                            <div class="punishment-card <?= $cardClass ?>">
                                <div class="punishment-card-inner">
                                    <div class="punishment-header"
                                         data-bs-toggle="collapse" data-bs-target="#punishment<?= $index ?>"
                                         aria-expanded="false" aria-controls="punishment<?= $index ?>">
                                        <div>
                                            <div class="d-flex align-items-center">
                                                <span class="punishment-type-badge <?= $typeBadgeClass ?>">
                                                    <i class="fas <?= $iconClass ?>"></i>
                                                    <?= $punishmentType ?>
                                                </span>

                                                <span class="punishment-status <?= $isActive ? 'active' : 'expired' ?>">
                                                    <i class="fas <?= $isActive ? 'fa-circle' : 'fa-circle-notch' ?>"></i>
                                                    <?= $isActive ? 'Active' : 'Expired' ?>
                                                </span>
                                            </div>


                                        </div>

                                        <div class="punishment-date">
                                            <?= $issuedDate ?>
                                            <i class="fas fa-chevron-down"></i>
                                        </div>
                                    </div>

                                    <div class="collapse" id="punishment<?= $index ?>">
                                        <div class="punishment-content">

                                            <?php

                                            $violationClass = $punishmentType === 'BAN' ? 'ban-violation' : ($punishmentType === 'MUTE' ? 'mute-violation' : 'warning-violation');


                                            $violationIcon = $punishmentType === 'BAN' ? 'fa-ban' : ($punishmentType === 'MUTE' ? 'fa-volume-mute' : 'fa-exclamation-circle');


                                            if ($punishmentType === 'BAN') {
                                                $violationText = 'Ban';
                                            } elseif ($punishmentType === 'MUTE') {
                                                $violationText = 'Mute';
                                            } elseif ($punishmentType === 'WARNING' || $punishmentType === 'WARN') {
                                                $violationText = 'Warning';
                                            } else {
                                                $violationText = htmlspecialchars($offenseTypeDisplay);
                                            }
                                            ?>

                                            <!-- Issued For Box -->
                                            <div class="punishment-issued-for <?= $violationClass ?>">
                                                <div class="punishment-issued-for-title">
                                                    <i class="fas fa-gavel"></i>
                                                    This punishment was issued for:
                                                </div>
                                                <div class="punishment-issued-for-text">
                                                    <?php

                                                    error_log("Punishment type: " . $punishmentType);
                                                    error_log("Offense type: " . $offenseType);
                                                    error_log("Offense count: " . $offenseCount);
                                                    error_log("Punishment reason: " . ($punishment['reason'] ?? 'Not set'));


                                                    if (($punishmentType === 'BAN' || $punishmentType === 'MUTE') && !empty($punishment['reason'])) {
                                                        echo htmlspecialchars($punishment['reason']);
                                                        error_log("Showing {$punishmentType} reason: " . $punishment['reason']);
                                                    }

                                                    elseif (!empty($offenseType)) {

                                                        if (stripos($offenseType, 'hacking') !== false || stripos($offenseType, 'cheating') !== false) {
                                                            echo "Violating server rules";
                                                        } else {

                                                            $formattedOffenseType = ucwords(str_replace('_', ' ', $offenseType));
                                                            echo htmlspecialchars($formattedOffenseType);


                                                            error_log("Formatted offense type: " . $formattedOffenseType);
                                                        }
                                                    }

                                                    else {
                                                        echo "Violating server rules";
                                                    }
                                                    ?>
                                                </div>
                                            </div>

                                            <!-- Violation Type Box -->
                                            <?php if (!empty($offenseType) && $offenseCount > 0) { ?>
                                            <div class="punishment-violation <?= $violationClass ?>">
                                                <i class="fas <?= $violationIcon ?>"></i>
                                                <div>
                                                    <span class="punishment-violation-text"><?= $violationText ?></span>
                                                    <span class="punishment-offense-count">Offense #<?= $offenseCount ?></span>
                                                </div>
                                            </div>
                                            <?php } ?>

                                            <div class="punishment-details-grid">
                                                <div class="punishment-detail-item">
                                                    <i class="fas fa-calendar-plus text-info"></i>
                                                    <strong>Issued:</strong>
                                                    <span class="punishment-detail-value"><?= $issuedDate ?></span>
                                                </div>

                                                <?php if ($expiresDate) { ?>
                                                <div class="punishment-detail-item">
                                                    <i class="fas fa-calendar-times <?= $isActive ? 'text-warning' : 'text-muted' ?>"></i>
                                                    <strong>Expires:</strong>
                                                    <span class="punishment-detail-value">
                                                        <?php

                                                        echo convertToET($expiresDate);


                                                        $now = time();
                                                        $expiryDate = null;

                                                        if (is_object($expiresDate)) {
                                                            if (method_exists($expiresDate, 'toDateTime')) {
                                                                $expiryDate = $expiresDate->toDateTime()->getTimestamp();
                                                            }
                                                        } else if (is_string($expiresDate)) {
                                                            $expiryDate = strtotime($expiresDate);
                                                        }

                                                        if ($expiryDate && $expiryDate > $now) {
                                                            $timeLeft = $expiryDate - $now;
                                                            $days = floor($timeLeft / 86400);
                                                            $hours = floor(($timeLeft % 86400) / 3600);

                                                            echo " <span class=\"text-warning\">(";
                                                            if ($days > 0) echo "$days days, ";
                                                            echo "$hours hours remaining)</span>";
                                                        } else if ($expiryDate) {
                                                            echo " <span class=\"text-muted\">(Expired)</span>";
                                                        }
                                                        ?>
                                                    </span>
                                                </div>
                                                <?php } ?>

                                                <?php if (isset($punishment['duration'])) { ?>
                                                <div class="punishment-detail-item">
                                                    <i class="fas <?= $punishment['duration'] === 'permanent' ? 'fa-infinity text-danger' : 'fa-hourglass-half text-info' ?>"></i>
                                                    <strong>Duration:</strong>
                                                    <span class="punishment-detail-value">
                                                        <?php
                                                        $duration = $punishment['duration'];
                                                        if ($duration === 'permanent') {
                                                            echo 'Permanent';
                                                        } else if (preg_match('/^(\d+)([dhm])$/', $duration, $matches)) {
                                                            $value = $matches[1];
                                                            $unit = $matches[2];
                                                            switch ($unit) {
                                                                case 'd': echo "$value day" . ($value > 1 ? 's' : ''); break;
                                                                case 'h': echo "$value hour" . ($value > 1 ? 's' : ''); break;
                                                                case 'm': echo "$value minute" . ($value > 1 ? 's' : ''); break;
                                                            }
                                                        } else {
                                                            echo htmlspecialchars($duration);
                                                        }
                                                        ?>
                                                    </span>
                                                </div>
                                                <?php } ?>
                                            </div>

                                            <!-- Punishment ID removed as requested -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                    <?php } ?>
                </div>
            </div> <!-- End player-content -->
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js"></script>
    <script>

        window.onerror = function(message, source, lineno, colno, error) {
            console.error('JavaScript error:', message, 'at', source, lineno, colno);
            console.error('Error object:', error);
            return false;
        };

        document.addEventListener('DOMContentLoaded', function() {

            console.log('DOM loaded, showing player content');
            var loadingElement = document.getElementById('loading');
            var contentElement = document.getElementById('player-content');

            if (loadingElement) {
                loadingElement.style.display = 'none';
                console.log('Loading element hidden');
            } else {
                console.error('Loading element not found');
            }

            if (contentElement) {
                contentElement.style.display = 'block';
                console.log('Content element shown');
            } else {
                console.error('Content element not found');
            }
        });

        function triggerConfetti(position) {
            if (position >= 16 && position <= 200) {
                confetti({
                    particleCount: 10,
                    spread: 20,
                    origin: { y: 0.8 },
                    colors: ['#808080'], // Gray color for a less exciting effect
                    shapes: ['circle']
                });
            } else if (position >= 1 && position <= 15) {
                confetti({
                    particleCount: Math.floor(Math.random() * 100) + 50, // Random between 50 and 150
                    spread: Math.floor(Math.random() * 60) + 40, // Random between 40 and 100
                    origin: { y: Math.random() * 0.4 + 0.4 }, // Random between 0.4 and 0.8
                    colors: ['#FF4500', '#32CD32', '#1E90FF', '#FFD700'],
                    shapes: ['circle', 'square']
                });
            }
        }


    </script>

    } catch (Exception $e) {
        renderHeader('Database Error');
        renderNavbar();

        echo "<div class='container text-center mt-5' id='api-error-container'>
                <div class='alert alert-danger'>
                    <h1>Database Error</h1>
                    <p id='api-error-message'>There was an error loading player data.</p>
                    <p class='small text-muted'>Please try again later or contact support if the issue persists.</p>
                </div>
                <div class='mt-4'>
                    <button class='btn btn-primary me-2' onclick='window.location.href=\"/search\"'>Back to Search</button>
                    <button class='btn btn-outline-secondary' onclick='window.location.href=\"/\"'>Return to Home</button>
                </div>
              </div>";

        renderFooter([
            'https://cdn.jsdelivr.net/npm/sweetalert2@11',
            '/js/search.min.js?v=1'
        ]);
        exit;
    }

    <?php renderFooter([
        'https://cdn.jsdelivr.net/npm/sweetalert2@11',
        'https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css',
        '/js/search.min.js?v=1',
        '/js/punishment-redesign.min.js?v=1'
    ]); ?>
    <?php
} else {
    header('Location: /search');
    exit;
}
?>