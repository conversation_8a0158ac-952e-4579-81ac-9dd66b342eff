<?php
require_once __DIR__ . '/includes/layout-unified.php';
require_once __DIR__ . '/includes/db_access.php';
require_once __DIR__ . '/includes/mongo_security.php';


function formatNumber($number) {
    return number_format($number);
}

function formatBalance($balance) {
    return "$" . number_format($balance);
}

/**
 * Convert UUIDs to usernames for faction members
 */
function resolveUuidsToUsernames($uuids, $db) {
    if (empty($uuids) || !is_array($uuids)) {
        return [];
    }

    $resolved = [];

    foreach ($uuids as $uuid) {
        if (empty($uuid)) {
            continue;
        }

        try {
            $player = null;
            error_log("Trying to resolve UUID: " . $uuid);

            // Try multiple search strategies based on how UUIDs might be stored
            if (is_string($uuid)) {
                // Strategy 1: Search by xuid field (string UUID)
                $player = $db->player_data->findOne(['xuid' => $uuid], [
                    'projection' => ['username' => 1, 'playernames' => 1]
                ]);
                error_log("Strategy 1 (xuid search) result: " . ($player ? 'found' : 'not found'));

                // Strategy 2: Search by uuid field as string
                if (!$player) {
                    $player = $db->player_data->findOne(['uuid' => $uuid], [
                        'projection' => ['username' => 1, 'playernames' => 1]
                    ]);
                }

                // Strategy 3: Convert string UUID to binary format for database query
                if (!$player) {
                    $uuid_clean = str_replace('-', '', $uuid);
                    if (strlen($uuid_clean) === 32) {
                        $uuid_binary = new MongoDB\BSON\Binary(hex2bin($uuid_clean), MongoDB\BSON\Binary::TYPE_UUID);
                        $player = $db->player_data->findOne(['uuid' => $uuid_binary], [
                            'projection' => ['username' => 1, 'playernames' => 1]
                        ]);
                    }
                }
            } else {
                // Handle Binary UUID objects directly
                $player = $db->player_data->findOne(['uuid' => $uuid], [
                    'projection' => ['username' => 1, 'playernames' => 1]
                ]);
            }

            if ($player) {
                // Try to get username from different possible fields
                $username = null;
                if (isset($player['username'])) {
                    $username = $player['username'];
                } elseif (isset($player['playernames']) && is_array($player['playernames']) && !empty($player['playernames'])) {
                    $username = $player['playernames'][0]; // Use first playernames entry
                }

                if ($username) {
                    $resolved[] = $username;
                } else {
                    // If username not found, show a truncated UUID as fallback
                    $uuid_str = is_string($uuid) ? $uuid : (
                        $uuid instanceof MongoDB\BSON\Binary ? bin2hex($uuid->getData()) : 'unknown'
                    );
                    $resolved[] = 'Player_' . substr($uuid_str, 0, 8);
                }
            } else {
                // If player not found, show a truncated UUID as fallback
                $uuid_str = is_string($uuid) ? $uuid : (
                    $uuid instanceof MongoDB\BSON\Binary ? bin2hex($uuid->getData()) : 'unknown'
                );
                $resolved[] = 'Player_' . substr($uuid_str, 0, 8);
            }
        } catch (Exception $e) {
            error_log("Error resolving UUID to username: " . $e->getMessage());
            // Fallback to showing partial UUID
            $uuid_str = is_string($uuid) ? $uuid : 'unknown';
            $resolved[] = 'Player_' . substr($uuid_str, 0, 8);
        }
    }

    return $resolved;
}


$factionName = isset($_GET['name']) ?
    sanitize_mongo_input($_GET['name'], 'string', [
        'max_length' => 100,
        'allow_empty' => false
    ]) : null;


if (!$factionName) {
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));
    if (count($pathParts) >= 2 && $pathParts[0] === 'faction') {

        $factionName = sanitize_mongo_input(urldecode($pathParts[1]), 'string', [
            'max_length' => 100,
            'allow_empty' => false
        ]);
    }
}

$pageTitle = $factionName ? "$factionName - Faction Profile" : "Faction Profile";
$faction = null;
$error = null;

if ($factionName) {
    try {
        // Use API client to get up-to-date faction data
        require_once __DIR__ . '/includes/api_client.php';
        $apiClient = new ApiClient();
        $faction = $apiClient->getFactionData($factionName);

        if (isset($faction['error'])) {
            $error = $faction['error'];
            error_log("API Error loading faction {$factionName}: " . $faction['error']);
        } else {
            error_log("Successfully loaded faction {$factionName} from API");
            // Debug: Log what fields the API returned
            error_log("API returned faction fields: " . implode(', ', array_keys($faction)));

            // Map Go API field names to expected field names for compatibility
            if (isset($faction['BankDoubloons'])) {
                $faction['bankdoubloons'] = $faction['BankDoubloons'];
            }
            if (isset($faction['Strength'])) {
                $faction['strength'] = $faction['Strength'];
            }
            if (isset($faction['Members'])) {
                $faction['members'] = $faction['Members'];
                $faction['memberCount'] = count($faction['Members']);

                // Resolve member UUIDs to usernames
                try {
                    require_once __DIR__ . '/includes/db_access.php';
                    $db = new DatabaseAccess();
                    error_log("Faction members from API: " . json_encode($faction['Members']));
                    $faction['memberUsernames'] = resolveUuidsToUsernames($faction['Members'], $db->db);
                    error_log("Resolved member usernames: " . json_encode($faction['memberUsernames']));
                } catch (Exception $e) {
                    error_log("Error resolving member UUIDs: " . $e->getMessage());
                    $faction['memberUsernames'] = [];
                }
            }
            if (isset($faction['Allies'])) {
                $faction['allies'] = $faction['Allies'];
            }
            if (isset($faction['Name'])) {
                $faction['name'] = $faction['Name'];
            }
        }
    } catch (Exception $e) {
        error_log("Exception loading faction {$factionName}: " . $e->getMessage());
        $error = "Error loading faction data: " . $e->getMessage();
    }
} else {
    $error = "No faction name provided";
}

renderHeader($pageTitle, ['/css/faction-new.css']);
renderNavbar();
?>
<div class="container">
    <div class="card bg-dark text-light border-0 shadow-lg">
        <header class="text-center mb-4 position-relative">
            <div class="faction-banner rounded-top p-4" style="background: linear-gradient(135deg, #1e2a45, #101828);">
                <h1 class="display-5 fw-bold text-light mb-0" id="faction-name">
                    <?php echo $factionName ? $factionName : 'Faction Profile'; ?>
                </h1>
                <p class="text-secondary mb-0 mt-2"><i class="fas fa-shield-alt me-2"></i>Faction Details</p>
            </div>
        </header>

        <?php if ($error): ?>
        <div id="error-message" class="text-center py-5">
            <div class="alert alert-danger mx-auto" style="max-width: 600px;">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <h4 class="alert-heading">Faction Not Found</h4>
                <p>This faction is no longer found in our database.</p>
                <hr>
                <p class="mb-0">This faction may have been removed or disbanded.</p>
                <p class="small text-muted mt-2">If you believe this is an error, please contact support.</p>
            </div>
            <div class="mt-4">
                <a href="/search" class="btn btn-primary me-2">Search for Factions</a>
                <a href="/" class="btn btn-outline-secondary">Return to Home</a>
            </div>
        </div>
        <?php elseif (!$faction): ?>
        <div id="loading" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="text-light mt-3">Loading faction data...</p>
        </div>
        <?php else: ?>
        <div id="faction-details" class="card-body p-0">
            <div class="leadership-section px-4 pt-2 pb-4 mb-4 border-bottom border-secondary">
                <h4 class="section-title"><i class="fas fa-chess-king me-2"></i>Faction Information</h4>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card bg-dark-secondary h-100 border-0">
                            <div class="card-header border-bottom border-secondary">
                                <h5 class="mb-0"><i class="fas fa-info-circle text-info me-2"></i>Faction Name</h5>
                            </div>
                            <div class="card-body d-flex align-items-center">
                                <div class="faction-info">
                                    <span class="text-light">
                                        <i class="fas fa-flag me-2"></i><?php echo htmlspecialchars($faction['name'] ?? $factionName); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="card bg-dark-secondary h-100 border-0">
                            <div class="card-header border-bottom border-secondary">
                                <h5 class="mb-0"><i class="fas fa-calendar-plus text-success me-2"></i>Total Members</h5>
                            </div>
                            <div class="card-body d-flex align-items-center">
                                <div class="member-info">
                                    <span class="text-light">
                                        <i class="fas fa-users me-2"></i><?php echo formatNumber($faction['memberCount'] ?? 0); ?> Members
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="stats-overview px-4 pb-4 mb-4 border-bottom border-secondary">
                <div class="faction-summary-card mb-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="faction-power d-flex align-items-center">
                                <div class="power-icon">
                                    <i class="fas fa-fist-raised"></i>
                                </div>
                                <div class="power-details">
                                    <h6 class="power-label">Faction Strength</h6>
                                    <p class="power-value" id="faction-strength">
                                        <?php echo formatNumber(round($faction['strength'] ?? 0)); ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="faction-treasury d-flex align-items-center">
                                <div class="treasury-icon">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <div class="treasury-details">
                                    <h6 class="treasury-label">Treasury</h6>
                                    <p class="treasury-value" id="faction-balance">
                                        <?php echo formatBalance($faction['bankdoubloons'] ?? 0); ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <h4 class="section-title"><i class="fas fa-chart-line me-2"></i>Faction Stats</h4>
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-details">
                                <h6 class="stat-title">Members</h6>
                                <p class="stat-value" id="member-count">
                                    <?php echo formatNumber($faction['memberCount'] ?? 0); ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-handshake"></i>
                            </div>
                            <div class="stat-details">
                                <h6 class="stat-title">Allies</h6>
                                <p class="stat-value" id="ally-count">
                                    <?php
                                    $allyCount = 0;
                                    if (!empty($faction['allies'])) {
                                        if (is_array($faction['allies']) || $faction['allies'] instanceof \MongoDB\Model\BSONDocument) {
                                            $allies = $faction['allies'];
                                            if ($allies instanceof \MongoDB\Model\BSONDocument) {
                                                $allies = (array)$allies;
                                            }
                                            $allyCount = count($allies);
                                        }
                                    }
                                    echo formatNumber($allyCount);
                                    ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="stat-details">
                                <h6 class="stat-title">Officers</h6>
                                <p class="stat-value" id="officer-count">
                                    <?php echo formatNumber(count($faction['officers'] ?? [])); ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="faction-relations px-4 pb-4">
                <h4 class="section-title"><i class="fas fa-network-wired me-2"></i>Members & Relations</h4>
                <div class="row">

                    <div class="col-md-6 mb-4">
                        <div class="card bg-dark-secondary h-100 border-0">
                            <div class="card-header border-bottom border-secondary">
                                <h5 class="mb-0"><i class="fas fa-user text-primary me-2"></i>Members</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="list-container" style="max-height: 300px; overflow-y: auto;">
                                    <?php if (!empty($faction['memberUsernames'])): ?>
                                    <ul id="members-list" class="list-group list-group-flush player-list">
                                        <?php foreach ($faction['memberUsernames'] as $memberUsername): ?>
                                        <li class="list-group-item bg-dark text-light">
                                            <a href="/player.php?username=<?php echo urlencode($memberUsername); ?>" class="text-decoration-none text-light">
                                                <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($memberUsername); ?>
                                            </a>
                                        </li>
                                        <?php endforeach; ?>
                                    </ul>
                                    <?php else: ?>
                                    <p id="no-members" class="text-center p-3 text-secondary">
                                        No regular members
                                    </p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card bg-dark-secondary h-100 border-0">
                            <div class="card-header border-bottom border-secondary">
                                <h5 class="mb-0"><i class="fas fa-handshake text-success me-2"></i>Allies</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="list-container" style="max-height: 300px; overflow-y: auto;">
                                    <?php
                                    $has_allies = false;
                                    if (!empty($faction['allies'])) {
                                        if (is_array($faction['allies']) || $faction['allies'] instanceof \MongoDB\Model\BSONDocument) {

                                            $allies = $faction['allies'];
                                            if ($allies instanceof \MongoDB\Model\BSONDocument) {
                                                $allies = (array)$allies;
                                            }


                                            if (!empty($allies)) {
                                                $has_allies = true;
                                            }
                                        }
                                    }

                                    if ($has_allies):
                                    ?>
                                    <ul id="allies-list" class="list-group list-group-flush player-list">
                                        <?php foreach (array_keys($allies) as $ally): ?>
                                        <li class="list-group-item bg-dark text-light">
                                            <a href="/faction/<?php echo urlencode($ally); ?>" class="text-decoration-none text-light">
                                                <i class="fas fa-flag me-2"></i><?php echo htmlspecialchars($ally); ?>
                                            </a>
                                        </li>
                                        <?php endforeach; ?>
                                    </ul>
                                    <?php else: ?>
                                    <p id="no-allies" class="text-center p-3 text-secondary">
                                        No alliances formed
                                    </p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php
renderFooter([
    'https://cdn.jsdelivr.net/npm/sweetalert2@11',
    '/js/search.min.js?v=1'
]);
?>